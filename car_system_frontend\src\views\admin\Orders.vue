<template>
  <div class="admin-orders">
    <div class="page-header">
      <h1>工单管理</h1>
      <p>管理系统中的所有维修工单</p>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="filter-card">
      <el-form :model="filters" inline class="filter-form">
        <el-form-item label="工单号">
          <el-input
            v-model="filters.orderId"
            placeholder="请输入工单号"
            style="width: 150px"
            clearable
            @keyup.enter="fetchOrders"
          />
        </el-form-item>

        <el-form-item label="工单状态">
          <el-select
            v-model="filters.status"
            placeholder="全部状态"
            clearable
            style="width: 150px"
            @change="fetchOrders"
          >
            <el-option label="待处理" value="pending" />
            <el-option label="已分配" value="assigned" />
            <el-option label="已接受" value="accepted" />
            <el-option label="进行中" value="in_progress" />
            <el-option label="已完成" value="completed" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>

        <el-form-item label="紧急程度">
          <el-select
            v-model="filters.urgencyLevel"
            placeholder="全部程度"
            clearable
            style="width: 120px"
            @change="fetchOrders"
          >
            <el-option label="低" value="low" />
            <el-option label="中" value="normal" />
            <el-option label="高" value="high" />
            <el-option label="紧急" value="urgent" />
          </el-select>
        </el-form-item>

        <el-form-item label="故障类型">
          <el-select
            v-model="filters.faultTypeId"
            placeholder="全部类型"
            clearable
            style="width: 150px"
            @change="fetchOrders"
          >
            <el-option
              v-for="faultType in faultTypes"
              :key="faultType.faultTypeId"
              :label="faultType.typeName"
              :value="faultType.faultTypeId"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="工种">
          <el-select
            v-model="filters.specialty"
            placeholder="全部工种"
            clearable
            style="width: 150px"
            @change="fetchOrders"
          >
            <el-option
              v-for="specialty in specialties"
              :key="specialty.code"
              :label="specialty.name"
              :value="specialty.code"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="创建时间">
          <el-date-picker
            v-model="filters.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 240px"
            @change="fetchOrders"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="fetchOrders">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetFilters">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon pending">
            <el-icon size="24"><Clock /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ stats.pending }}</div>
            <div class="stat-label">待处理</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon progress">
            <el-icon size="24"><Tools /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ stats.inProgress }}</div>
            <div class="stat-label">进行中</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon completed">
            <el-icon size="24"><Check /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ stats.completed }}</div>
            <div class="stat-label">已完成</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon total">
            <el-icon size="24"><Document /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ stats.total }}</div>
            <div class="stat-label">总工单</div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 工单列表 -->
    <el-card>
      <!-- 移动端卡片视图 -->
      <div v-if="isMobile" class="mobile-order-list">
        <div v-for="order in orders" :key="order.orderId" class="mobile-order-card">
          <el-card shadow="hover" @click="viewOrderDetail(order)">
            <div class="order-info">
              <div class="order-header">
                <div class="order-id">工单 #{{ order.orderId }}</div>
                <el-tag :type="getStatusType(order.status)" size="small">
                  {{ getStatusText(order.status) }}
                </el-tag>
              </div>
              <div class="order-details">
                <div class="detail-item">
                  <span class="label">车辆:</span>
                  <span class="value">{{ order.vehicle?.licensePlate }} {{ order.vehicle?.brand }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">故障类型:</span>
                  <span class="value">{{ order.faultType?.typeName }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">用户:</span>
                  <span class="value">{{ order.user?.realName }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">技师:</span>
                  <span class="value">
                    <span v-if="order.assignedTechnicians?.length">
                      {{ order.assignedTechnicians.map(t => t.realName).join(', ') }}
                    </span>
                    <span v-else class="no-technician">未分配</span>
                  </span>
                </div>
                <div class="detail-item">
                  <span class="label">紧急程度:</span>
                  <span class="value">
                    <el-tag :type="getUrgencyType(order.urgencyLevel)" size="small">
                      {{ getUrgencyText(order.urgencyLevel) }}
                    </el-tag>
                  </span>
                </div>
                <div class="detail-item">
                  <span class="label">创建时间:</span>
                  <span class="value">{{ formatDate(order.submitTime) }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">总费用:</span>
                  <span class="value">{{ formatCost(order.totalCost, order.status) }}</span>
                </div>
              </div>
              <div class="order-actions">
                <el-button type="primary" size="small" @click.stop="viewOrderDetail(order)">
                  <el-icon><View /></el-icon>
                  查看详情
                </el-button>
                <el-button
                  v-if="order.status !== 'completed' && order.status !== 'cancelled'"
                  type="warning"
                  size="small"
                  @click.stop="showStatusDialog(order)"
                >
                  <el-icon><Edit /></el-icon>
                  修改状态
                </el-button>
              </div>
            </div>
          </el-card>
        </div>
      </div>

      <!-- 桌面端表格视图 -->
      <div v-else class="table-responsive">
        <el-table
          v-loading="loading"
          :data="orders"
          style="width: 100%"
          empty-text="暂无工单数据"
          stripe
          highlight-current-row
          @row-click="viewOrderDetail"
        >
          <el-table-column prop="orderId" label="工单号" width="100" />

          <el-table-column label="车辆信息" width="180">
            <template #default="{ row }">
              <div class="vehicle-info">
                <div class="license-plate">{{ row.vehicle?.licensePlate }}</div>
                <div class="vehicle-detail">
                  {{ row.vehicle?.brand }} {{ row.vehicle?.model }}
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="用户" width="120">
            <template #default="{ row }">
              {{ row.user?.realName }}
            </template>
          </el-table-column>

          <el-table-column prop="faultType.typeName" label="故障类型" width="120" />

          <el-table-column label="故障描述" min-width="200">
            <template #default="{ row }">
              <el-tooltip
                :content="row.description"
                placement="top"
                :disabled="row.description.length <= 50"
              >
                <div class="description-text">
                  {{ row.description.length > 50 ? row.description.substring(0, 50) + '...' : row.description }}
                </div>
              </el-tooltip>
            </template>
          </el-table-column>

          <el-table-column label="技师" width="150">
            <template #default="{ row }">
              <div v-if="row.assignedTechnicians?.length" class="technicians">
                <el-tag
                  v-for="tech in row.assignedTechnicians"
                  :key="tech.technicianId"
                  size="small"
                  style="margin-right: 4px; margin-bottom: 2px;"
                >
                  {{ tech.realName }}
                </el-tag>
              </div>
              <span v-else class="no-technician">未分配</span>
            </template>
          </el-table-column>

          <el-table-column label="紧急程度" width="100">
            <template #default="{ row }">
              <el-tag :type="getUrgencyType(row.urgencyLevel)" size="small">
                {{ getUrgencyText(row.urgencyLevel) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)" size="small">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="创建时间" width="160">
            <template #default="{ row }">
              {{ formatDate(row.submitTime) }}
            </template>
          </el-table-column>

          <el-table-column label="总费用" width="100" align="center">
            <template #default="{ row }">
              {{ formatCost(row.totalCost, row.status) }}
            </template>
          </el-table-column>

          <el-table-column label="操作" width="180" fixed="right">
            <template #default="{ row }">
              <el-button type="text" size="small" @click.stop="viewOrderDetail(row)">
                <el-icon><View /></el-icon>
                查看详情
              </el-button>
              <el-button
                v-if="row.status !== 'completed' && row.status !== 'cancelled'"
                type="text"
                size="small"
                @click.stop="showStatusDialog(row)"
              >
                <el-icon><Edit /></el-icon>
                修改状态
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="fetchOrders"
          @current-change="fetchOrders"
        />
      </div>
    </el-card>

    <!-- 修改状态对话框 -->
    <el-dialog
      v-model="statusDialog.visible"
      title="修改工单状态"
      width="500px"
      @close="resetStatusForm"
    >
      <el-form
        ref="statusFormRef"
        :model="statusForm"
        :rules="statusRules"
        label-width="100px"
      >
        <el-form-item label="当前状态">
          <el-tag :type="getStatusType(statusDialog.currentStatus)" size="small">
            {{ getStatusText(statusDialog.currentStatus) }}
          </el-tag>
        </el-form-item>

        <el-form-item label="新状态" prop="status">
          <el-select
            v-model="statusForm.status"
            placeholder="请选择新状态"
            style="width: 100%"
          >
            <el-option label="待处理" value="pending" />
            <el-option label="已分配" value="assigned" />
            <el-option label="已接受" value="accepted" />
            <el-option label="进行中" value="in_progress" />
            <el-option label="已完成" value="completed" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>

        <el-form-item label="修改原因" prop="reason">
          <el-input
            v-model="statusForm.reason"
            type="textarea"
            :rows="3"
            placeholder="请输入修改状态的原因"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="statusDialog.visible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="submitting"
          @click="submitStatusForm"
        >
          确定修改
        </el-button>
      </template>
    </el-dialog>

    <!-- 工单详情对话框 -->
    <el-dialog
      v-model="detailDialog.visible"
      title="工单详情"
      width="800px"
    >
      <div v-if="selectedOrder" class="order-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="工单号">
            #{{ selectedOrder.orderId }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(selectedOrder.status)" size="small">
              {{ getStatusText(selectedOrder.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="车辆">
            {{ selectedOrder.vehicle?.licensePlate }} {{ selectedOrder.vehicle?.brand }} {{ selectedOrder.vehicle?.model }}
          </el-descriptions-item>
          <el-descriptions-item label="用户">
            {{ selectedOrder.user?.realName }}
          </el-descriptions-item>
          <el-descriptions-item label="故障类型">
            {{ selectedOrder.faultType?.typeName }}
          </el-descriptions-item>
          <el-descriptions-item label="紧急程度">
            <el-tag :type="getUrgencyType(selectedOrder.urgencyLevel)" size="small">
              {{ getUrgencyText(selectedOrder.urgencyLevel) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDate(selectedOrder.submitTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="预计完成时间">
            {{ formatDate(selectedOrder.estimatedCompletionTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="实际完成时间">
            {{ selectedOrder.actualCompletionTime ? formatDate(selectedOrder.actualCompletionTime) : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="总费用">
            {{ formatCost(selectedOrder.totalCost, selectedOrder.status) }}
          </el-descriptions-item>
          <el-descriptions-item label="故障描述" :span="2">
            {{ selectedOrder.description }}
          </el-descriptions-item>
        </el-descriptions>

        <div v-if="selectedOrder.assignedTechnicians?.length" class="technicians-section">
          <h4>分配技师</h4>
          <el-table :data="selectedOrder.assignedTechnicians" style="width: 100%">
            <el-table-column prop="realName" label="姓名" />
            <el-table-column prop="specialty" label="工种">
              <template #default="{ row }">
                {{ getSpecialtyName(row.specialty) }}
              </template>
            </el-table-column>
            <el-table-column prop="phone" label="联系电话" />
          </el-table>
        </div>

        <!-- 材料使用记录 -->
        <div v-if="selectedOrder.materialUsages && selectedOrder.materialUsages.length > 0" class="materials-section">
          <MaterialUsageCard
            :material-usages="selectedOrder.materialUsages"
            :show-summary="true"
          />
        </div>

        <!-- 催单信息 -->
        <div v-if="selectedOrder.urgentRequests && selectedOrder.urgentRequests.length > 0" class="urgent-section">
          <h4>
            <el-icon><Bell /></el-icon>
            用户催单记录 ({{ selectedOrder.urgentRequests.length }}条)
          </h4>
          <div class="urgent-list">
            <div
              v-for="urgent in selectedOrder.urgentRequests"
              :key="urgent.urgentId"
              class="urgent-item"
            >
              <div class="urgent-header">
                <span class="urgent-time">{{ formatDate(urgent.urgentTime) }}</span>
                <el-tag :type="urgent.status === 'pending' ? 'danger' : 'success'" size="small">
                  {{ urgent.statusDisplayName }}
                </el-tag>
              </div>
              <div class="urgent-content">
                <p class="urgent-reason">{{ urgent.reason }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 用户反馈 -->
        <div v-if="selectedOrder.feedback" class="feedback-section">
          <h4>用户反馈</h4>
          <div class="feedback-content">
            <div class="feedback-rating">
              <span>评分: </span>
              <el-rate
                v-model="selectedOrder.feedback.rating"
                disabled
                show-score
                text-color="#ff9900"
              />
            </div>
            <div v-if="selectedOrder.feedback.content" class="feedback-text">
              <p>{{ selectedOrder.feedback.content }}</p>
            </div>
            <div class="feedback-time">
              反馈时间: {{ formatDate(selectedOrder.feedback.feedbackTime) }}
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search, Refresh, Clock, Tools, Check, Document, View, Edit, Bell
} from '@element-plus/icons-vue'
import { adminAPI } from '@/api/admin'
import { faultTypeAPI } from '@/api/faultType'
import { technicianAPI } from '@/api/technician'
import { orderAPI } from '@/api/order'
import { useAppStore } from '@/stores/app'
import MaterialUsageCard from '@/components/MaterialUsageCard.vue'
import dayjs from 'dayjs'

const appStore = useAppStore()

// 计算属性
const isMobile = computed(() => appStore.isMobile)

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const orders = ref([])
const faultTypes = ref([])
const specialties = ref([])
const selectedOrder = ref(null)

// 表单引用
const statusFormRef = ref()

// 分页数据
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 筛选条件
const filters = reactive({
  orderId: '',
  status: '',
  urgencyLevel: '',
  faultTypeId: null,
  specialty: '',
  dateRange: null
})

// 统计数据
const stats = reactive({
  pending: 0,
  inProgress: 0,
  completed: 0,
  total: 0
})

// 状态对话框
const statusDialog = reactive({
  visible: false,
  orderId: null,
  currentStatus: ''
})

// 详情对话框
const detailDialog = reactive({
  visible: false
})

// 状态表单
const statusForm = reactive({
  status: '',
  reason: ''
})

// 状态表单验证规则
const statusRules = {
  status: [
    { required: true, message: '请选择新状态', trigger: 'change' }
  ],
  reason: [
    { required: true, message: '请输入修改原因', trigger: 'blur' },
    { min: 5, message: '修改原因至少5个字符', trigger: 'blur' }
  ]
}

// 工种映射
const specialtyMap = {
  engine: { name: '发动机维修', type: 'primary' },
  transmission: { name: '变速箱维修', type: 'success' },
  brake: { name: '制动系统维修', type: 'warning' },
  electrical: { name: '电气系统维修', type: 'danger' },
  hvac: { name: '空调系统维修', type: 'info' },
  chassis: { name: '底盘维修', type: '' },
  body: { name: '车身维修', type: 'primary' },
  tire: { name: '轮胎维修', type: 'success' }
}

// 获取工单列表
const fetchOrders = async () => {
  try {
    loading.value = true

    const params = {
      page: pagination.page,
      size: pagination.size
    }

    // 添加搜索条件
    if (filters.orderId) params.orderId = filters.orderId
    if (filters.status) params.status = filters.status
    if (filters.urgencyLevel) params.urgencyLevel = filters.urgencyLevel
    if (filters.faultTypeId) params.faultTypeId = filters.faultTypeId
    if (filters.specialty) params.specialty = filters.specialty
    if (filters.dateRange && filters.dateRange.length === 2) {
      params.startDate = filters.dateRange[0]
      params.endDate = filters.dateRange[1]
    }

    const response = await adminAPI.getAllOrders(params)
    orders.value = response.data.data?.content || []
    pagination.total = response.data.data?.page?.totalElements || 0

    // 更新统计数据
    updateStats()
  } catch (error) {
    console.error('Failed to fetch orders:', error)
    ElMessage.error('获取工单列表失败')
  } finally {
    loading.value = false
  }
}

// 获取故障类型列表
const fetchFaultTypes = async () => {
  try {
    const response = await faultTypeAPI.getList()
    faultTypes.value = response.data.data?.content || response.data || []
  } catch (error) {
    console.error('Failed to fetch fault types:', error)
  }
}

// 获取工种列表
const fetchSpecialties = async () => {
  try {
    const response = await technicianAPI.getSpecialties()
    const specialtyList = response.data || []

    // 安全地转换为选项格式，处理可能的对象或字符串数据
    specialties.value = specialtyList.map(item => {
      // 如果 item 是字符串，直接使用
      if (typeof item === 'string') {
        return {
          code: item,
          name: specialtyMap[item]?.name || item
        }
      }
      // 如果 item 是对象，提取 code 属性
      else if (typeof item === 'object' && item !== null) {
        const code = item.code || item
        return {
          code: code,
          name: specialtyMap[code]?.name || code
        }
      }
      // 其他情况，使用默认值
      else {
        return {
          code: item,
          name: String(item)
        }
      }
    })
  } catch (error) {
    console.error('Failed to fetch specialties:', error)
    // 使用默认工种列表
    specialties.value = Object.keys(specialtyMap).map(code => ({
      code,
      name: specialtyMap[code].name
    }))
  }
}

// 更新统计数据
const updateStats = () => {
  stats.pending = orders.value.filter(order => order.status === 'pending' || order.status === 'assigned').length
  stats.inProgress = orders.value.filter(order => order.status === 'accepted' || order.status === 'in_progress').length
  stats.completed = orders.value.filter(order => order.status === 'completed').length
  stats.total = orders.value.length
}

// 重置筛选条件
const resetFilters = () => {
  Object.assign(filters, {
    orderId: '',
    status: '',
    urgencyLevel: '',
    faultTypeId: null,
    specialty: '',
    dateRange: null
  })
  pagination.page = 1
  fetchOrders()
}

// 查看工单详情
const viewOrderDetail = (order) => {
  selectedOrder.value = order
  detailDialog.visible = true
}

// 显示状态修改对话框
const showStatusDialog = (order) => {
  statusDialog.orderId = order.orderId
  statusDialog.currentStatus = order.status
  statusDialog.visible = true
  resetStatusForm()
}

// 重置状态表单
const resetStatusForm = () => {
  Object.assign(statusForm, {
    status: '',
    reason: ''
  })

  // 清除表单验证
  if (statusFormRef.value) {
    statusFormRef.value.clearValidate()
  }
}

// 提交状态修改
const submitStatusForm = async () => {
  try {
    // 表单验证
    if (!statusFormRef.value) return
    await statusFormRef.value.validate()

    submitting.value = true

    await orderAPI.updateStatus(statusDialog.orderId, {
      status: statusForm.status,
      reason: statusForm.reason
    })

    ElMessage.success('工单状态修改成功')
    statusDialog.visible = false
    fetchOrders()
  } catch (error) {
    console.error('Failed to update order status:', error)
    ElMessage.error('修改工单状态失败')
  } finally {
    submitting.value = false
  }
}

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    pending: 'info',
    assigned: 'warning',
    accepted: 'primary',
    in_progress: 'warning',
    completed: 'success',
    cancelled: 'danger'
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    pending: '待处理',
    assigned: '已分配',
    accepted: '已接受',
    in_progress: '进行中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

// 获取紧急程度类型
const getUrgencyType = (urgency) => {
  const urgencyMap = {
    low: 'info',
    normal: '',
    high: 'warning',
    urgent: 'danger'
  }
  return urgencyMap[urgency] || ''
}

// 获取紧急程度文本
const getUrgencyText = (urgency) => {
  const urgencyMap = {
    low: '低',
    normal: '中',
    high: '高',
    urgent: '紧急'
  }
  return urgencyMap[urgency] || urgency
}

// 获取工种名称
const getSpecialtyName = (specialty) => {
  return specialtyMap[specialty]?.name || specialty
}

// 格式化费用显示
const formatCost = (cost, status) => {
  // 如果订单未完成，显示"待结算"
  if (status !== 'completed') {
    return '待结算'
  }
  // 如果订单已完成，显示实际费用
  return `¥${cost || 0}`
}

// 格式化日期
const formatDate = (date) => {
  if (!date) return '-'
  return dayjs(date).format('YYYY-MM-DD HH:mm')
}

// 组件挂载时获取数据
onMounted(() => {
  fetchOrders()
  fetchFaultTypes()
  fetchSpecialties()
})
</script>

<style scoped>
.admin-orders {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.filter-card {
  margin-bottom: 20px;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.filter-form .el-form-item {
  margin-bottom: 0;
}

/* 统计卡片 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.stat-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 8px 0;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
}

.stat-icon.pending {
  background: linear-gradient(135deg, #409eff, #66b1ff);
}

.stat-icon.progress {
  background: linear-gradient(135deg, #e6a23c, #f0c78a);
}

.stat-icon.completed {
  background: linear-gradient(135deg, #67c23a, #95d475);
}

.stat-icon.total {
  background: linear-gradient(135deg, #909399, #b1b3b8);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

/* 移动端工单卡片 */
.mobile-order-list {
  display: grid;
  gap: 16px;
}

.mobile-order-card {
  cursor: pointer;
}

.order-info {
  padding: 0;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.order-id {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.order-details {
  margin-bottom: 16px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-item .label {
  color: #606266;
  font-weight: 500;
  min-width: 80px;
}

.detail-item .value {
  color: #303133;
  text-align: right;
  flex: 1;
}

.no-technician {
  color: #909399;
  font-style: italic;
}

.order-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 桌面端表格 */
.table-responsive {
  overflow-x: auto;
}

.vehicle-info {
  display: flex;
  flex-direction: column;
}

.license-plate {
  font-weight: 600;
  color: #303133;
  margin-bottom: 2px;
}

.vehicle-detail {
  font-size: 12px;
  color: #909399;
}

.description-text {
  line-height: 1.4;
}

.technicians {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

/* 分页 */
.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* 工单详情 */
.order-detail {
  padding: 0;
}

.technicians-section {
  margin-top: 24px;
}

.technicians-section h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.materials-section {
  margin-top: 24px;
}

.urgent-section {
  margin-top: 24px;
}

.urgent-section h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.urgent-section h4 .el-icon {
  color: #f56c6c;
}

.urgent-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.urgent-item {
  padding: 16px;
  background: #fef0f0;
  border-radius: 8px;
  border-left: 4px solid #f56c6c;
}

.urgent-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.urgent-time {
  font-size: 14px;
  color: #909399;
}

.urgent-content {
  margin: 0;
}

.urgent-reason {
  color: #303133;
  line-height: 1.6;
  margin: 0;
  font-weight: 500;
}

.feedback-section {
  margin-top: 24px;
}

.feedback-section h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.feedback-content {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.feedback-rating {
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.feedback-text {
  margin-bottom: 12px;
}

.feedback-text p {
  margin: 0;
  color: #606266;
  line-height: 1.6;
}

.feedback-time {
  font-size: 14px;
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .admin-orders {
    padding: 16px;
  }

  .filter-form {
    flex-direction: column;
  }

  .filter-form .el-form-item {
    width: 100%;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .stat-content {
    flex-direction: column;
    text-align: center;
  }

  .stat-icon {
    margin-right: 0;
    margin-bottom: 8px;
  }

  .order-actions {
    justify-content: center;
  }

  .pagination-container {
    overflow-x: auto;
  }
}

@media (max-width: 480px) {
  .admin-orders {
    padding: 12px;
  }

  .page-header h1 {
    font-size: 20px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .order-actions .el-button {
    flex: 1;
    min-width: 0;
  }

  .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .detail-item .value {
    text-align: left;
  }
}
</style>
